# Python
**/__pycache__/
**/*.py[cod]
**/*.so
**/*.egg
**/*.egg-info/
**/dist/
**/build/
**/*.pyo
**/*.pyc

# Virtualenv
**/venv/
**/ENV/
**/env/
**/*.venv
pip-log.txt
pip-delete-this-directory.txt

# Node
**/node_modules/
**/npm-debug.log
**/yarn-debug.log
**/yarn-error.log
**/.pnp/
**/.pnp.js

# Yarn
**/.yarn/*
!**/.yarn/cache
!**/.yarn/patches
!**/.yarn/releases
!**/.yarn/plugins
!**/.yarn/sdks
!**/.yarn/versions

# Bun
**/.bun/

# Rust
**/target/
Cargo.lock

# OS generated files
**/.DS_Store
**/Thumbs.db

# Logs and databases
**/*.log
**/*.sql
**/*.sqlite

# IDEs and editors
**/.vscode/
**/.idea/
**/*.sublime-workspace
**/*.sublime-project

# Environment variables
**/.env
**/.env.*
**images
**load_data.py
**media/
**media/**
**/media/

**db.sqlite3

**__pycache__
**static

# Django migrations
**/migrations/*.py
# Migrations
**/migrations/
# Except for __init__.py files in migrations directories
!**/migrations/__init__.py

**docker-compose.yml
**newupdatecategories.py

products/management/commands/clean_products.py
products/management/commands/__init__.py
products/management/__init__.py
update_16052025_products.csv
test_order_emails.py
test_email.py
json_to_csv_converter.py
triumph-static-assets/
test_email.py
test_order_emails.py
json_to_csv_converter.py
products/management/__init__.py
products/management/commands/__init__.py
products/management/__init__.py
update_16052025_products.csv
products/management/commands/clean_products.py
import_qubo_products.py
export_today_products.py
cleanup_duplicate_images.py
media_path_update_report_20250521_142243.csv
media_path_update_report_20250521_142858.csv
update_media_paths.py

product_dirs.txt
import_haier_products.py
fix_backslash_paths.py

test_api_gst_response.py
test_dynamic_gst.py
test_email_gst_fix.py
test_email_gst_rate_fix.py
test_gst_calculation_fix.py
test_gst_functionality.py
test_gst_inclusive.py
test_gst_rules.py
test_gst_with_sqlite.py
test_invoice_download.py
test_invoice_fix_complete.py
test_invoice_gst_fix.py
test_invoice_gst_rules.py
 .coverage
GST_INCLUSIVE_IMPLEMENTATION.md
GST_RULES_IMPLEMENTATION_SUMMARY.md
INVOICE_DOWNLOAD_FIX_SUMMARY.md
package-lock.json
products/management/commands/setup_default_gst.py
simple_gst_test.py


DATABASE_ENCRYPTION_IMPLEMENTATION_PLAN.md
ENCRYPTION_QUICK_START.md
PRODUCTION_DEPLOYMENT_CHECKLIST.md
PRODUCTION_ENV_VARIABLES_COMPLETE.md
PRODUCTION_KEYS_GENERATION_GUIDE.md
PRODUCTION_SECURITY_DEPLOYMENT_GUIDE.md
QUICK_START_ENCRYPTION_GUIDE.md
SECURITY_EMAIL_NOTIFICATIONS.md
FINAL_TEST_RESULTS.md
TEST_ISOLATION_FIXES.md
PRODUCTION_FIX_REPORT.md
test_critical_apis.py
test_security_fix.py
.coverage
ENHANCED_SECURITY_MONITORING_REPORT.md
SECURITY_MONITORING_FIXES_SUMMARY.md
test_browser_detection.py
test_login_request_fix.py
FORGOT_PASSWORD_IMPLEMENTATION.md
FRONTEND_URL_FIX_SUMMARY.md
frontend_integration_example.html
test_frontend_url_fix.py
test_password_reset.py


SECURITY_FIX_SUMMARY.md
test_improved_messages.py

test_comprehensive_fix.py
test_ip_blocking_bug.py
test_ip_blocking_fix.py
IP_BLOCKING_BUG_FIX_SUMMARY.md
security_audit_report.json



BACKEND_CAPACITY_SUMMARY.md
THROTTLING_CONFIGURATION_SUMMARY.md
analyze_backend_capacity.py
analyze_vps_capacity.py
calculate_daily_users.py
test_final_solution.py
test_permanent_block.py
test_throttling_config.py
test_user_different_ip.py



DATABASE_CONSISTENCY_GUIDE.md
DATABASE_REPLICA_OPTIMIZATION_GUIDE.md
DATABASE_SYNC_GUIDE.md

BACKUP_RESTORE_GUIDE.md
SYNC_DATABASES_FIX.md



test_order_packaging.py
test_weight_impact.py
update_product_weights.py
demo_packaging_system.py
shipping/management/commands/test_order_packaging.py
shipping/management/commands/test_packaging_system.py
demo_packaging_system.py
API_Documentation_ Rapidshyp.md


.htaccess
passenger_wsgi.py
tmp
WEIGHT_UPDATE_README.md
fix_missing_products.py
update_weights.py
weight_update_preview.csv
weight_update_report.csv
tests/test_shipping_haier_weight_limits.py
verify_implementation.py


test_product_update.py
test_sync_fix.py
verify_sync_working.py