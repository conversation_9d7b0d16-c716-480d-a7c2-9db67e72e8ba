#!/usr/bin/env python
"""
Quick verification script to check if database sync is working
Run this after updating a product in the admin panel
"""

import os
import django
from django.conf import settings

# Setup Django (minimal)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

try:
    django.setup()
    
    from products.models import Product
    from backend.db_sync import DatabaseSynchronizer
    
    print("🔍 Checking Database Sync Status")
    print("=" * 40)
    
    # Get replica databases
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    
    if not replica_dbs:
        print("❌ No replica databases configured")
        exit()
    
    print(f"📊 Replica databases: {replica_dbs}")
    
    # Check a few products
    products = Product.objects.all()[:3]
    
    for product in products:
        print(f"\n🧪 Product {product.pk}: {product.name}")
        
        # Check if it exists in all replicas
        for db_name in replica_dbs:
            try:
                replica_product = Product.objects.using(db_name).get(pk=product.pk)
                print(f"  ✅ {db_name}: Found (updated: {replica_product.updated_at})")
            except Product.DoesNotExist:
                print(f"  ❌ {db_name}: Not found")
            except Exception as e:
                print(f"  ⚠️ {db_name}: Error - {e}")
    
    print("\n📊 Sync Status Check Complete")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("Make sure you have the required dependencies installed")
