#!/usr/bin/env python
"""
Test script to verify database synchronization fix
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from products.models import Product, Category, Brand
from backend.db_sync import DatabaseSynchronizer

def test_sync_fix():
    """Test the database synchronization fix"""
    print("🔧 Testing Database Synchronization Fix")
    print("=" * 50)
    
    # Get replica databases
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    
    if not replica_dbs:
        print("❌ No replica databases configured")
        return False
    
    print(f"📊 Found replica databases: {replica_dbs}")
    
    # Get a test product
    try:
        test_product = Product.objects.first()
        if not test_product:
            print("❌ No products found to test with")
            return False
        
        print(f"🧪 Testing with Product ID: {test_product.pk} - {test_product.name}")
        print(f"   Category: {test_product.category}")
        print(f"   Brand: {test_product.brand}")
        
        # Test the sync manually
        print("\n🔄 Testing manual sync...")
        try:
            DatabaseSynchronizer.sync_model_to_replicas(test_product, 'save')
            print("✅ Manual sync completed successfully!")
            
            # Verify the sync worked
            for db_name in replica_dbs:
                try:
                    replica_product = Product.objects.using(db_name).get(pk=test_product.pk)
                    print(f"✅ Product {test_product.pk} found in {db_name}")
                    print(f"   Name: {replica_product.name}")
                    print(f"   Category ID: {replica_product.category_id}")
                    print(f"   Brand ID: {replica_product.brand_id}")
                except Product.DoesNotExist:
                    print(f"❌ Product {test_product.pk} NOT found in {db_name}")
                    return False
                except Exception as e:
                    print(f"❌ Error checking {db_name}: {e}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Manual sync failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def test_product_update():
    """Test updating a product to trigger sync"""
    print("\n🔄 Testing Product Update Sync")
    print("=" * 50)
    
    try:
        test_product = Product.objects.first()
        if not test_product:
            print("❌ No products found to test with")
            return False
        
        print(f"🧪 Updating Product ID: {test_product.pk}")
        
        # Store original description
        original_description = test_product.description
        
        # Update the product
        test_description = f"Test sync update - {original_description}"
        test_product.description = test_description
        test_product.save()
        
        print(f"✅ Product updated with description: {test_description}")
        
        # Check if it synced to replicas
        replica_dbs = DatabaseSynchronizer.get_replica_databases()
        
        for db_name in replica_dbs:
            try:
                replica_product = Product.objects.using(db_name).get(pk=test_product.pk)
                if replica_product.description == test_description:
                    print(f"✅ Sync verified in {db_name}")
                else:
                    print(f"❌ Sync failed in {db_name}")
                    print(f"   Expected: {test_description}")
                    print(f"   Got: {replica_product.description}")
                    return False
            except Exception as e:
                print(f"❌ Error checking {db_name}: {e}")
                return False
        
        # Restore original description
        test_product.description = original_description
        test_product.save()
        
        return True
        
    except Exception as e:
        print(f"❌ Error during product update test: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Database Sync Tests")
    print("=" * 50)
    
    # Test 1: Manual sync
    test1_result = test_sync_fix()
    
    # Test 2: Product update sync
    test2_result = test_product_update()
    
    print("\n📊 Test Results")
    print("=" * 50)
    print(f"Manual Sync Test: {'✅ PASSED' if test1_result else '❌ FAILED'}")
    print(f"Product Update Test: {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed! Database sync is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
