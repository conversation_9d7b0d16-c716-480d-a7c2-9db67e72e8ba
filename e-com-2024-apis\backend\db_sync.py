"""
Database Synchronization for Product Data

This module provides automatic synchronization of product data
(products, categories, brands) across primary and replica databases.
"""

import logging
from django.db import transaction, connections
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.conf import settings
from django.core.management.color import no_style
from django.db import connection
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DatabaseSynchronizer:
    """Handle database synchronization between primary and replicas"""

    @staticmethod
    @contextmanager
    def _bypass_router():
        """Context manager to temporarily bypass database router restrictions"""
        # Set sync flag to allow relations
        original_sync_flag = getattr(settings, '_SYNC_IN_PROGRESS', False)

        try:
            # Set flag to allow cross-database relations during sync
            settings._SYNC_IN_PROGRESS = True
            yield
        finally:
            # Restore original flag
            settings._SYNC_IN_PROGRESS = original_sync_flag

    @staticmethod
    def _sync_with_raw_sql(model_class, pk, field_values, db_name):
        """Use raw SQL to sync data, bypassing Django's ORM restrictions"""
        table_name = model_class._meta.db_table
        conn = connections[db_name]

        # Prepare field names and values for SQL
        field_names = list(field_values.keys())
        field_placeholders = ['%s'] * len(field_names)
        values = list(field_values.values())

        # Use PostgreSQL's ON CONFLICT for upsert
        with conn.cursor() as cursor:
            # Build the upsert query
            insert_sql = f"""
                INSERT INTO {table_name} (id, {', '.join(field_names)})
                VALUES (%s, {', '.join(field_placeholders)})
                ON CONFLICT (id) DO UPDATE SET
                {', '.join([f'{field} = EXCLUDED.{field}' for field in field_names])}
            """

            cursor.execute(insert_sql, [pk] + values)

    @staticmethod
    def _delete_with_raw_sql(model_class, pk, db_name):
        """Use raw SQL to delete data, bypassing Django's ORM restrictions"""
        table_name = model_class._meta.db_table
        conn = connections[db_name]

        with conn.cursor() as cursor:
            cursor.execute(f"DELETE FROM {table_name} WHERE id = %s", [pk])

    @staticmethod
    def get_replica_databases():
        """Get list of replica database names"""
        replica_dbs = []
        for db_name in settings.DATABASES.keys():
            if db_name != 'default' and 'read' in db_name.lower():
                replica_dbs.append(db_name)
        return replica_dbs
    
    @staticmethod
    def sync_model_to_replicas(instance, operation='save'):
        """
        Sync a model instance to all replica databases

        Args:
            instance: Model instance to sync
            operation: 'save' or 'delete'
        """
        replica_dbs = DatabaseSynchronizer.get_replica_databases()

        if not replica_dbs:
            logger.debug("No replica databases configured for sync")
            return

        model_class = instance.__class__

        for db_name in replica_dbs:
            try:
                if operation == 'save':
                    # Use bypass context to allow cross-database relations
                    with DatabaseSynchronizer._bypass_router():
                        # Prepare field values for ORM operations
                        field_values = {}
                        for field in model_class._meta.fields:
                            if field.name != 'id':  # Skip primary key
                                field_values[field.name] = getattr(instance, field.name)

                        # Try ORM approach first (now that router allows it)
                        try:
                            replica_instance, created = model_class.objects.using(db_name).get_or_create(
                                pk=instance.pk,
                                defaults=field_values
                            )

                            # If not created, update the existing instance
                            if not created:
                                for field_name, field_value in field_values.items():
                                    setattr(replica_instance, field_name, field_value)
                                replica_instance.save(using=db_name)

                            logger.debug(f"Synced {model_class.__name__} {instance.pk} to {db_name}")

                        except Exception as orm_error:
                            # Fallback to raw SQL if ORM still fails
                            logger.debug(f"ORM sync failed, trying raw SQL: {orm_error}")

                            # Prepare field values for raw SQL, handling foreign keys
                            sql_field_values = {}
                            for field in model_class._meta.fields:
                                if field.name != 'id':
                                    field_value = getattr(instance, field.name)

                                    # Handle foreign key relationships
                                    if hasattr(field, 'related_model') and field.related_model is not None:
                                        if field_value is not None:
                                            if hasattr(field_value, 'pk'):
                                                sql_field_values[field.column] = field_value.pk
                                            else:
                                                sql_field_values[field.column] = field_value
                                        else:
                                            sql_field_values[field.column] = None
                                    else:
                                        sql_field_values[field.column] = field_value

                            DatabaseSynchronizer._sync_with_raw_sql(
                                model_class, instance.pk, sql_field_values, db_name
                            )
                            logger.debug(f"Synced {model_class.__name__} {instance.pk} to {db_name} using raw SQL")

                elif operation == 'delete':
                    # Delete the instance from replica
                    with DatabaseSynchronizer._bypass_router():
                        try:
                            # Try ORM delete first
                            deleted_count = model_class.objects.using(db_name).filter(pk=instance.pk).delete()[0]
                            if deleted_count > 0:
                                logger.debug(f"Deleted {model_class.__name__} {instance.pk} from {db_name}")
                            else:
                                logger.debug(f"{model_class.__name__} {instance.pk} not found in {db_name}")
                        except Exception as delete_error:
                            # Fallback to raw SQL
                            logger.debug(f"ORM delete failed, trying raw SQL: {delete_error}")
                            try:
                                DatabaseSynchronizer._delete_with_raw_sql(
                                    model_class, instance.pk, db_name
                                )
                                logger.debug(f"Deleted {model_class.__name__} {instance.pk} from {db_name} using raw SQL")
                            except Exception as sql_error:
                                logger.debug(f"Raw SQL delete also failed: {sql_error}")

            except Exception as e:
                logger.error(f"Failed to sync {model_class.__name__} {instance.pk} to {db_name}: {e}")
    
    @staticmethod
    def bulk_sync_to_replicas(model_class, queryset=None):
        """
        Bulk sync all instances of a model to replicas
        
        Args:
            model_class: Model class to sync
            queryset: Optional queryset to sync (defaults to all objects)
        """
        replica_dbs = DatabaseSynchronizer.get_replica_databases()
        
        if not replica_dbs:
            logger.info("No replica databases configured for bulk sync")
            return
        
        if queryset is None:
            queryset = model_class.objects.all()
        
        total_objects = queryset.count()
        logger.info(f"Starting bulk sync of {total_objects} {model_class.__name__} objects")
        
        for db_name in replica_dbs:
            try:
                # Clear existing data in replica
                model_class.objects.using(db_name).all().delete()
                
                # Bulk create in replica
                objects_to_create = []
                for obj in queryset:
                    # Create a copy without pk to force creation
                    obj_copy = model_class()
                    for field in model_class._meta.fields:
                        if field.name != 'id':  # Skip primary key
                            setattr(obj_copy, field.name, getattr(obj, field.name))
                    objects_to_create.append(obj_copy)
                
                model_class.objects.using(db_name).bulk_create(objects_to_create)
                logger.info(f"Bulk synced {len(objects_to_create)} {model_class.__name__} objects to {db_name}")
                
            except Exception as e:
                logger.error(f"Failed to bulk sync {model_class.__name__} to {db_name}: {e}")


# Signal handlers for automatic synchronization
@receiver(post_save, sender='products.Product')
def sync_product_save(sender, instance, created, **kwargs):
    """Sync product saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync product {instance.pk}: {e}")


@receiver(post_delete, sender='products.Product')
def sync_product_delete(sender, instance, **kwargs):
    """Sync product deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync product deletion {instance.pk}: {e}")


@receiver(post_save, sender='products.Category')
def sync_category_save(sender, instance, created, **kwargs):
    """Sync category saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync category {instance.pk}: {e}")


@receiver(post_delete, sender='products.Category')
def sync_category_delete(sender, instance, **kwargs):
    """Sync category deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync category deletion {instance.pk}: {e}")


@receiver(post_save, sender='products.Brand')
def sync_brand_save(sender, instance, created, **kwargs):
    """Sync brand saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync brand {instance.pk}: {e}")


@receiver(post_delete, sender='products.Brand')
def sync_brand_delete(sender, instance, **kwargs):
    """Sync brand deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync brand deletion {instance.pk}: {e}")


# Additional models that should be synced
@receiver(post_save, sender='products.SubCategorie')
def sync_subcategory_save(sender, instance, created, **kwargs):
    """Sync subcategory saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync subcategory {instance.pk}: {e}")


@receiver(post_delete, sender='products.SubCategorie')
def sync_subcategory_delete(sender, instance, **kwargs):
    """Sync subcategory deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync subcategory deletion {instance.pk}: {e}")


@receiver(post_save, sender='products.GST')
def sync_gst_save(sender, instance, created, **kwargs):
    """Sync GST saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync GST {instance.pk}: {e}")


@receiver(post_delete, sender='products.GST')
def sync_gst_delete(sender, instance, **kwargs):
    """Sync GST deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync GST deletion {instance.pk}: {e}")


def enable_sync():
    """Enable database synchronization"""
    if hasattr(settings, '_DISABLE_SYNC'):
        delattr(settings, '_DISABLE_SYNC')
    logger.info("Database synchronization enabled")


def disable_sync():
    """Disable database synchronization (useful for bulk operations)"""
    settings._DISABLE_SYNC = True
    logger.info("Database synchronization disabled")


def sync_status():
    """Check if synchronization is enabled"""
    return not (hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC)
