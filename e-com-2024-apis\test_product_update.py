#!/usr/bin/env python
"""
Simple test to update a product and check if sync works
Run this with: python manage.py shell < test_product_update.py
"""

from products.models import Product
from backend.db_sync import DatabaseSynchronizer
import traceback

print("🔧 Testing Product Update Sync Fix")
print("=" * 50)

try:
    # Check replica databases
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    print(f"📊 Replica databases: {replica_dbs}")
    
    if not replica_dbs:
        print("❌ No replica databases configured")
        exit()
    
    # Get a test product
    test_product = Product.objects.first()
    if not test_product:
        print("❌ No products found")
        exit()
    
    print(f"🧪 Testing with Product ID: {test_product.pk}")
    print(f"   Name: {test_product.name}")
    print(f"   Category: {test_product.category}")
    print(f"   Brand: {test_product.brand}")
    
    # Store original description
    original_description = test_product.description or ""
    
    # Update the product description
    test_description = f"SYNC TEST - {original_description[:50]}"
    test_product.description = test_description
    
    print(f"\n🔄 Updating product description to: {test_description}")
    
    # Save the product (this should trigger the sync)
    test_product.save()
    print("✅ Product saved to primary database")
    
    # Check if it synced to replicas
    print("\n🔍 Checking replica databases...")
    
    all_synced = True
    for db_name in replica_dbs:
        try:
            replica_product = Product.objects.using(db_name).get(pk=test_product.pk)
            if replica_product.description == test_description:
                print(f"✅ {db_name}: Sync successful")
            else:
                print(f"❌ {db_name}: Sync failed")
                print(f"   Expected: {test_description}")
                print(f"   Got: {replica_product.description}")
                all_synced = False
        except Product.DoesNotExist:
            print(f"❌ {db_name}: Product not found")
            all_synced = False
        except Exception as e:
            print(f"❌ {db_name}: Error - {e}")
            all_synced = False
    
    # Restore original description
    test_product.description = original_description
    test_product.save()
    print(f"\n🔄 Restored original description")
    
    # Final result
    print("\n📊 Test Result")
    print("=" * 50)
    if all_synced:
        print("🎉 SUCCESS: Database sync is working correctly!")
    else:
        print("⚠️ FAILED: Database sync is not working properly")
        
except Exception as e:
    print(f"❌ Error during test: {e}")
    print("Traceback:")
    traceback.print_exc()
